export default interface InputDateTypes {
  id: string
  className?: string
  inputClass?: string
  labelClass?: string
  isRequired?: boolean
  label?: string
  onChange?: (selectedDate: any) => void
  format?: string
  menuClassName?: string
  placement?: any
  error?: any
  errorClass?: string
  cleanable?: boolean
  value?: any
  defaultValue?: any
  placeholder?: string
  isLoading?: boolean
  isDisabled?: boolean
  isReadOnly?: boolean
  onBlur?: Function
  control?: any
  registerKey?: string
  rules?: any
  floatingElement?: boolean
  autoFocus?: boolean
  disableFutureDate?: boolean
  [key: string]: any
}
