
import React, {useState} from 'react'
import {Meta, StoryFn} from '@storybook/react'
import ConfirmationModal, {ConfirmationModalDialog} from './ConfirmationModal'

// Default export for storybook metadata
export default {
  title: 'Components/ConfirmationModal',
  component: ConfirmationModal,
  argTypes: {
    uniqueID: {control: 'text'},
    show: {control: 'boolean'},
    onClose: {action: 'closed'},
    onAction: {action: 'action'},
    isOperationLoading: {control: 'boolean'},
    isDataLoading: {control: 'boolean'},
    body: {control: 'text'},
    actionName: {control: 'text'},
    actionBtnClass: {control: 'text'},
    title: {control: 'text'},
    bodyClass: {control: 'text'},
    disableAction: {control: 'boolean'},
  },
} as Meta

// Template for the component stories
const Template: StoryFn<ConfirmationModalDialog> = (args) => {
  const [show, setShow] = useState(args.show)

  const handleClose = () => {
    setShow(false)
    args.onClose()
  }

  const handleAction = (uniqueID: any) => {
    args.onAction?.(uniqueID)
  }

  return <ConfirmationModal {...args} show={show} onClose={handleClose} onAction={handleAction} />
}

// Default story
export const Default = Template.bind({})
Default.args = {
  show: true,
  title: 'Are you sure?',
  body: 'Do you really want to delete this record? This action is irreversible.',
  actionName: 'Yes',
  actionBtnClass: 'btn-danger',
}

// Loading state story
export const LoadingState = Template.bind({})
LoadingState.args = {
  show: true,
  title: 'Loading...',
  isOperationLoading: true,
  body: 'Please wait while the operation completes.',
  disableAction: true,
}

// With uniqueID story
export const WithUniqueID = Template.bind({})
WithUniqueID.args = {
  uniqueID: '12345',
  show: true,
  title: 'Confirmation',
  body: 'Do you want to proceed with the action for item #12345?',
  actionName: 'Confirm',
  actionBtnClass: 'btn-primary',
}
