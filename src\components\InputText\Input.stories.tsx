// src/Input.stories.tsx

import React from 'react'
import {Meta, StoryFn} from '@storybook/react'
import Input from './InputText'
import {InputTypes} from './InputTypes'

export default {
  title: 'Components/Input',
  component: Input,
} as Meta

const Template: StoryFn<InputTypes> = (args) => <Input {...args} />

export const Default = Template.bind({})
Default.args = {
  id: 'default',
  name: 'default',
  placeholder: 'Enter text',
  label: 'Default Input',
  isRequired: false,
  register: () => {}, // Mock register function
}

export const Required = Template.bind({})
Required.args = {
  id: 'required',
  name: 'required',
  placeholder: 'Enter text',
  label: 'Required Input',
  isRequired: true,
  register: () => {}, // Mock register function
}

export const WithError = Template.bind({})
WithError.args = {
  id: 'with-error',
  name: 'with-error',
  placeholder: 'Enter text',
  label: 'Input with <PERSON>rror',
  isRequired: true,
  error: {message: 'This field is required'},
  register: () => {}, // Mock register function
}
