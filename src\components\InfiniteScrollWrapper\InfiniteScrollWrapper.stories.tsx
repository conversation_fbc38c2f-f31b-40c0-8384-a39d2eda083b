
import { Meta, StoryFn } from '@storybook/react';
import InfiniteScrollWrapper, { InfiniteScrollWrapperProps } from './InfiniteScrollWrapper';

export default {
  title: 'Components/InfiniteScrollWrapper',
  component: InfiniteScrollWrapper,
  argTypes: {
    name: { control: 'text' },
    height: { control: 'number' },
    loadMore: { action: 'loadMore' },
  },
} as Meta;

const Template: StoryFn<InfiniteScrollWrapperProps> = (args) => (
  <InfiniteScrollWrapper {...args}>
    {args.data?.map((item, index) => (
      <div key={index} style={{ padding: '10px', border: '1px solid #ccc', marginBottom: '5px' }}>
        {item}
      </div>
    ))}
  </InfiniteScrollWrapper>
);

export const Default = Template.bind({});
Default.args = {
  name: 'example',
  height: 300,
  data: Array.from({ length: 20 }, (_, i) => `Item ${i + 1}`),
  loaderComponent: <div>Loading...</div>,
};


export const EmptyList = Template.bind({});
EmptyList.args = {
  name: 'empty-list',
  height: 300,
  data: [],
  loaderComponent: <div>Loading...</div>,
};
