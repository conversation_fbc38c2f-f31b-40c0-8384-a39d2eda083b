import {Link} from 'react-router-dom'
import OverlayComponent from '../../_metronic/layout/components/Popover'
import {DynamicTableActionOverlayProps} from './dynamicTableTypes'

const DynamicTableActionOverlay = ({
  actionMenus,
  hasWritePermission,
}: DynamicTableActionOverlayProps) => {
  const menuItem = (menu: any, index: number) => {
    return (
      <div
        key={index}
        className='px-2 py-1'
        onClick={() => {
          menu.onClick?.()
          document.body.click()
        }}
      >
        <div className='text-dark fw-bold text-hover-primary fs-6'>
          <label>{menu.label}</label>
        </div>
      </div>
    )
  }

  const renders: JSX.Element[] = []
  const overlayItems: JSX.Element[] = []

  actionMenus.forEach((menu: any, index: any) => {
    if (menu.render) {
      renders.push(<div key={`render-${index}`}>{menu.render}</div>)
    }
    const menuElement = (menu.checkPermission ? hasWritePermission : true) ? (
      <>
        {(menu.show ?? true) && (
          <>
            {menu.isLink && menu.to ? (
              <Link key={`link-${index}`} to={menu.to} {...(menu.state && {state: menu.state})}>
                {menuItem(menu, index)}
              </Link>
            ) : (
              menuItem(menu, index)
            )}
          </>
        )}
      </>
    ) : (
      <></>
    )
    overlayItems.push(menuElement)
  })

  return (
    <td>
      <div className='d-flex justify-content-center align-items-center flex-shrink-0'>
        <OverlayComponent
          btnIcon={<i className='las la-ellipsis-h fs-2x'></i>}
          children={overlayItems}
        />
        {renders}
      </div>
    </td>
  )
}

export default DynamicTableActionOverlay
