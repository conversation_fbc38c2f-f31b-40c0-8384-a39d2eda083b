
import { Meta, StoryFn } from '@storybook/react';
import DndTreeView from './DndTreeView';
import { DndTreeViewProps } from './interfaces';

export default {
  title: 'Example/DndTreeView',
  component: DndTreeView,
  parameters: {
    layout: 'centered',
  },
} as Meta;

const Template: StoryFn<DndTreeViewProps> = (args) => <DndTreeView {...args} />;

export const Primary = Template.bind({});
Primary.args = {
  data: [
    {
      id: '1',
      title: 'Root 1',
      children: [
        { id: '1-1', title: 'Child 1-1' },
        { id: '1-2', title: 'Child 1-2' },
      ],
    },
    {
      id: '2',
      title: 'Root 2',
      children: [
        { id: '2-1', title: 'Child 2-1' },
        { id: '2-2', title: 'Child 2-2' },
      ],
    },
  ],
  treeStructure: (item : any) => <div>{item.title}</div>,
  onChange: (updatedData) => {
    console.log('Updated Data:', updatedData);
  },
  setCheckbox: true,
  setHeaderCheckbox: true,
  disableSorting: false,
  autoSelectChildren: false,
  indentedCheckbox: false,
  isMultiSelect: false,
  getUpdatedDataOnly: false,
  onSelectCheckbox: (checked) => {
    console.log('Checkbox Selected:', checked);
  },
  indentationWidth: 50,
};

export const WithMultiSelect = Template.bind({});
WithMultiSelect.args = {
  ...Primary.args,
  isMultiSelect: true,
  autoSelectChildren: true,
};

export const WithoutCheckbox = Template.bind({});
WithoutCheckbox.args = {
  ...Primary.args,
  setCheckbox: false,
};
