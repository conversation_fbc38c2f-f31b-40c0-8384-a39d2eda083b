export const svgFileName = (module: string): string => {
  const moduleToFileMap: Record<string, string> = {
    Order: 'icon-order.svg',
    'Bulk Order': 'icon-bulk-order.svg',
    'Live Cart': 'icon-live-cart.svg',
    'Price List': 'icon-price-list.svg',
    'Product Inquiry': 'icon-product-inquiry.svg',
    Ticket: 'icon-ticket.svg',
    User: 'icon-user.svg',
    'Project Management': 'icon-project-management.svg',
    Settings: 'icon-setting.svg',
    Products: 'icon-product.svg',
    'Block Order': 'icon-block-order.svg',
    Customers:'icon-customer.svg',
    Default: 'icon-notification.svg',
  }
  

  return moduleToFileMap[module] || moduleToFileMap['Default']
}
