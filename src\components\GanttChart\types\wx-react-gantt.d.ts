declare module 'wx-react-gantt' {
  export interface GanttTask {
    id: number
    text: string
    start: Date
    end: Date
    duration: number
    progress: number
    type: string
    lazy?: boolean
    parent?: number
  }

  export interface GanttLink {
    id: number
    source: number
    target: number
    type: string
  }

  interface GanttProps {
    tasks: GanttTask[]
    links: GanttLink[]
    scales: Array<{
      unit: string
      step: number
      format: string
    }>
  }

  export const Gantt: React.FC<GanttProps>
}
