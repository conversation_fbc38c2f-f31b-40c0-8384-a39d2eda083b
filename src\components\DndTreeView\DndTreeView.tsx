import React, {useEffect, useState, useCallback, useMemo} from 'react'
import {TreeItemComponentProps} from './types/index'
import {SortableTree} from './components/sortableTree/SortableTree'
import {
  getChangedData,
  handleCheckboxChange,
  formatData,
  transformDataToCheckboxValues,
  handleAllCheckboxes,
} from './utils/index'
import {TableTreeItemWrapper} from './components/wrappers/TableTreeItemWrapper'
import {DndTreeViewProps} from './interfaces'

const DndTreeView = ({
  data,
  treeStructure,
  onChange,
  setCheckbox = false,
  setHeaderCheckbox = true,
  disableSorting = false,
  autoSelectChildren = false,
  indentedCheckbox = false,
  isMultiSelect = false,
  getUpdatedDataOnly = false,
  onSelectCheckbox,
  indentationWidth = 50,
  isHeadChecked,
  setIsHeadChecked,
  checkBoxValues,
  setCheckboxValues,
}: DndTreeViewProps) => {
  const [treeData, setTreeData] = useState(data)

  useEffect(() => {
    if (setCheckboxValues) {
      setCheckboxValues(transformDataToCheckboxValues(data, checkBoxValues))
    }
    setTreeData(data)
  }, [data, setCheckbox, setCheckboxValues, checkBoxValues])

  // Memoize treeData to avoid unnecessary recalculations
  const memoizedTreeData = useMemo(() => treeData, [treeData])

  const TreeItem = React.memo(
    React.forwardRef<HTMLDivElement, TreeItemComponentProps<any>>((props, ref) => {
      const wrapperProps = setCheckbox
        ? {
            isChecked: checkBoxValues,
            onCheckboxChange: (id: any) =>
              handleCheckboxChange(
                id,
                checkBoxValues,
                memoizedTreeData,
                autoSelectChildren,
                isMultiSelect,
                setCheckboxValues,
                onSelectCheckbox
              ),
            setCheckbox: setCheckbox,
            ...(indentedCheckbox && {indentedCheckbox: indentedCheckbox}),
          }
        : null

      return (
        <TableTreeItemWrapper {...props} ref={ref} {...wrapperProps}>
          {treeStructure(props?.item)}
        </TableTreeItemWrapper>
      )
    })
  )

  // Memoize head checkbox handler to avoid unnecessary re-renders
  const handleHeadCheckbox = useCallback(() => {
    if (setIsHeadChecked) {
      setIsHeadChecked(!isHeadChecked)
    }
    handleAllCheckboxes(
      checkBoxValues,
      !isHeadChecked,
      setCheckboxValues,
      isMultiSelect,
      onSelectCheckbox
    )
  }, [
    isHeadChecked,
    setIsHeadChecked,
    checkBoxValues,
    setCheckboxValues,
    isMultiSelect,
    onSelectCheckbox,
  ])

  // UseCallback to memoize the tree change handler
  const handleTreeChange = useCallback(
    (items: any) => {
      const onlyUpdatedData = getChangedData(data, formatData(items, data))
      if (onChange && onlyUpdatedData?.length > 0) {
        onChange(getUpdatedDataOnly ? onlyUpdatedData : formatData(items, data))
      }
      setTreeData(items)
    },
    [data, onChange, getUpdatedDataOnly]
  )

  return (
    <SortableTree
      items={memoizedTreeData}
      onItemsChanged={handleTreeChange}
      TreeItemComponent={TreeItem}
      disableSorting={disableSorting}
      indentationWidth={indentationWidth}
      setCheckbox={setCheckbox}
      setHeaderCheckbox={isMultiSelect ? setHeaderCheckbox : false}
      isHeadChecked={isHeadChecked}
      setIsHeadChecked={setIsHeadChecked}
      handleHeadCheckbox={handleHeadCheckbox}
    />
  )
}

export default DndTreeView
