export interface CheckboxTypes {
  id: string
  name?: string
  value?: string | number
  className?: string
  inputClass?: string
  label?: string | React.ReactNode
  register?: any
  isRequired?: boolean
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
  checked?: boolean
  defaultChecked?: boolean
  error?: any
  htmlFor?: string
  disabled?: boolean
  checkboxRef?: any
  control?: any
  isReadOnly?: boolean
  registerKey?: string
  labelClass?: string
}
