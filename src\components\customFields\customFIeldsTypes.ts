export interface Field {
  [key: string]: string | string[]
}

export interface CSVConfig {
  importConfig?: any
  downloadSampleCsv?: {
    sampleCsvFileName: string
    sampleCsvData: any[]
  }
}

export interface TableColumn {
  key: string
  label: string
  placeholder?: string
  maxLength?: number
  type?: string
  isMultiple?: boolean
  isDeletable?: boolean
  isDuplicate?: boolean
  isRequired?: boolean
  maxFields?: number
  style?: string
  checkDuplicatesAcrossRows?: boolean
  isReadOnly?: boolean
  isFloat?: boolean
  isCaseSensitive?: boolean
}

export interface TableConfig {
  data: TableColumn[]
  noDataMessage?: string
}

export interface DynamicMetaFieldsProps {
  id: string
  onChange?: (data: Field[]) => void
  defaultValues?: Field[]
  values?: Field[]
  onError?: Function
  className?: string
  headerClass?: string
  tableSectionClass?: string
  tableClass?: string
  isDisableAddBtn?: boolean
  readOnly?: boolean
  isDisabled?: boolean
  isDeletable?: boolean
  submitBtnRef?: any
  onSubmit?: any
  isRequired?: boolean
  config: {
    title: string
    tableConfig: TableConfig
    csvFile?: CSVConfig
    maxFields?: number
    enableMaxFieldMessage?: boolean
    addButtonLabel?: string
    deleteBtnLabel?: string
    isAddFieldOnRequiredError?: boolean
    titleClass?: string
  }
}
