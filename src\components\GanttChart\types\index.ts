import {GanttLink, GanttTask} from 'wx-react-gantt'

export interface GanttChartProps {
  data: any[]
  readOnly?: boolean
  links?: GanttLink[]
  scales?: Array<{
    unit: 'minute' | 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year'
    step: number
    format: any
    css: any
  }>
  tableColumns?: Array<{
    id: string
    header: string
    width?: number
    resize?: boolean
    sort?: boolean
    tree?: boolean
    template?: (value: any) => any
  }>
  taskDrag?: boolean
  taskResize?: boolean
  columnResize?: boolean
  sort?: boolean
  filter?: boolean
  keyboard?: boolean
  undo?: boolean
  zoom?: boolean | Record<string, any>
  multiselect?: boolean
  cellHeight?: number
  cellWidth?: number
  scaleHeight?: number
  onChange?: (task: GanttTask) => void
  onSortChartRows?: (task: GanttTask) => void
  childMoveBetweenGroups?: boolean
  isTaskCreatable?: boolean
  fullScreen?: boolean
  taskTemplate?: (task: GanttTask) => any
  weekends?: number[]
  highlightWeekend?: boolean
  startDate?: Date
  endDate?: Date
  cellBorders?: 'columns' | 'full'
  init?: (api: any) => void
  chartApisRef?: any
  isLoading?: boolean
  enableRowSelection?: boolean
}
