
import { Meta, StoryFn } from '@storybook/react';
import { useForm } from 'react-hook-form';
import InputNumber from './InputNumber';
import InputNumberTypes from './InputNumberTypes';

export default {
  title: 'Example/InputNumber',
  component: InputNumber,
  parameters: {
    layout: 'centered',
  },
} as Meta;

const Template: StoryFn<InputNumberTypes> = (args) => {
  const { register, formState: { errors } } = useForm();
  return (
    <InputNumber
      {...args}
      register={register(args.name ? args.name : '', { required: args.isRequired })}
      error={errors[args.name ? args.name : '']}
    />
  );
};

export const Primary = Template.bind({});
Primary.args = {
  id: 'input-number-primary',
  name: 'inputNumberPrimary',
  placeholder: 'Enter a number',
  label: 'Primary Input Number',
  isRequired: true,
  min: 1,
  max: 100,
};

export const Secondary = Template.bind({});
Secondary.args = {
  id: 'input-number-secondary',
  name: 'inputNumberSecondary',
  placeholder: 'Enter a number',
  label: 'Secondary Input Number',
  isRequired: false,
  min: 10,
  max: 200,
};
