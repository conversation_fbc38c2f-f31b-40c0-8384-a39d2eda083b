/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import {SerializedDocument} from '@lexical/file';
import <PERSON>o from 'pako';

async function readBytestoString(data: Uint8Array): Promise<string> {
  const output = [];
  const chunkSize = 0x8000;
  for (let i = 0; i < data.length; i += chunkSize) {
    output.push(String.fromCharCode(...Array.from(data.subarray(i, i + chunkSize))));
  }
  return output.join('');
}

export async function docToHash(doc: SerializedDocument): Promise<string> {
  const compressedData = Pako.gzip(JSON.stringify(doc));
  const output = await readBytestoString(compressedData);
  return `#doc=${btoa(output)
    .replace(/\//g, '_')
    .replace(/\+/g, '-')
    .replace(/=+$/, '')}`;
}

export async function docFromHash(
  hash: string
): Promise<SerializedDocument | null> {
  const m = /^#doc=(.*)$/.exec(hash);
  if (!m) {
    return null;
  }
  const b64 = atob(m[1].replace(/_/g, '/').replace(/-/g, '+'));
  const array = new Uint8Array(b64.length);
  for (let i = 0; i < b64.length; i++) {
    array[i] = b64.charCodeAt(i);
  }
  const decompressedData = Pako.ungzip(array);
  const jsonString = new TextDecoder().decode(decompressedData);
  return JSON.parse(jsonString);
}
