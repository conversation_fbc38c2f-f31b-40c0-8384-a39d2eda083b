export function calculateTasksSorting(tasks: any[]) {
  const taskIDs: any = {}

  for (const {moduleID, cID, ticketID} of tasks) {
    // Initialize the module ID if it doesn't exist
    if (!taskIDs[moduleID]) {
      taskIDs[moduleID] = {}
    }

    // Initialize the column ID if it doesn't exist
    if (!taskIDs[moduleID][cID]) {
      taskIDs[moduleID][cID] = []
    }

    // Push the ticket ID into the corresponding column array
    taskIDs[moduleID][cID].push(ticketID)
  }

  return taskIDs
}
