import Button from './Button'
import type {Meta, StoryObj} from '@storybook/react'

/**
 * These stories showcase the button
 */
const meta: Meta<typeof Button> = {
  title: 'From/Button',
  component: Button,
  argTypes: {
    variant: {
      options: ['primary', 'secondary', 'danger'],
      control: {type: 'radio'},
    },
  },
  tags: ['autodocs'],
}

type Story = StoryObj<typeof Button>

/**
 * This is the primary button
 */
export const primary: Story = {
  args: {
    variant: 'primary',
    children: 'Button',
  },
}

/**
 * This is the light button
 */
export const light: Story = {
  args: {
    variant: 'light',
    children: 'But<PERSON>',
  },
}

/**
 * This is the danger button
 */
export const danger: Story = {
  args: {
    variant: 'danger',
    children: 'Button',
  },
}

export default meta
