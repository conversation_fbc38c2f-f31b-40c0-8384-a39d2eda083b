import {useContext} from 'react'
import {DynamicTable} from '../../../../components/DynamicTable'
import {InStockNotifyContext} from '../../context'
import {inquiryDetailsTableColumns} from '../../utils'
import InquiryDetailsTableRow from './InquiryDetailsTableRow'

const InquiryDetailsTable = ({row, isParentRow}: any) => {
  const {productVariants, isOperationLoading} = useContext(InStockNotifyContext)

  return (
    <div className='popup-inquiry-table'>
     <DynamicTable
      data={productVariants}
      sortableColumns={inquiryDetailsTableColumns(isParentRow)}
      loading={isOperationLoading}
      tableClass='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2'
      TableRow={InquiryDetailsTableRow}
      tableRowProps={{isParentRow}}
    />
    </div>
    
   
  )
}
export default InquiryDetailsTable
