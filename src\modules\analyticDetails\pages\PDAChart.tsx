import {useParams} from 'react-router-dom'
import {useState} from 'react'

import {AnalyticsDetailsContext} from '../contexts'
import useGetProductSales from '../hooks/useGetProductSales'
import {useGetSoldProductOverviewData} from '../hooks/useGetSoldProductOverview'
import {useProductPerformanceData} from '../hooks/useGetProductPerformance'
import {useGetAllStateListData} from '../hooks/useGetAllStateList'
import {useGetTopCustomerData} from '../hooks/useGetTopCustomers'
import {useGetMainProductData} from '../hooks/useGetMainProduct'
import {useGetSKUReportData} from '../hooks/useGetSkuReport'
import {useGetDistributionData} from '../hooks/useGetDistribution'
import {useGetPriceReportData} from '../hooks/useGetPriceReport'

import Header from '../components/Header'
import Stats from '../components/Stats'
import Chart from '../components/Chart'
import {PriceReportTable} from '../sections/table/PriceReportTable'
import MainProductReportNav from '../components/MainProductReportNav'
import {SKUReportTable} from '../sections/table/SKUReportTable'
import {TopCustomersTable} from '../sections/table/TopCustomersTable'
import {parseOverviewData, processCustomerOverviewData} from '../utils'

function PDAChart() {
  const productName = localStorage ? localStorage.getItem('detailScreenProductName') : ''

  const params = useParams<any>()
  const productId = params?.id as string
  const [dateRangeLable, setDateRangeLable] = useState<string>('This Month')
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null,
  })

  const [topCustomerState, setTopCustomerState] = useState('')
  const [skuReportState, setSkuReportState] = useState('')

  const {data, onPeriodChange, onDateChange, options, chartType, setChartType} =
    useGetProductSales()
  const {overviewData, onDateChangeOverviewData, customerOverviewData} =
    useGetSoldProductOverviewData(productId)
  const {ProductPerformanceData, onDateChangeProductPerformance, isLoadingChartData} =
    useProductPerformanceData(productId)
  const {AllStateData, onStateDateChange} = useGetAllStateListData(productId)

  const {
    TopCustomerData: topCustomersData,
    isLoading: isTopCustomersLoading,
    onDateChange: onTopCustomersDateChange,
    onStateChange: onTopCustomersStateChange,
    topCustomer,
  } = useGetTopCustomerData({productId, state: topCustomerState})

  const {PriceReportData: priceReportData, isLoading: isPriceReportLoading} =
    useGetPriceReportData(productId)

  const {
    isLoading: isTopStateLoading,
    MainProductData: mainProductByState,
    topState,
  } = useGetMainProductData(productId)

  const {
    isLoading: isTopSkuLoading,
    SKUData: skuReportData,
    onStateChange: onSKUReportStateChange,
    topSku,
  } = useGetSKUReportData({productId, selectedValue: skuReportState})

  const {DistributionData, isLoading: isSkuDistributionLoading} = useGetDistributionData(productId)

  const parsedOverviewData = parseOverviewData(overviewData)
  const {orders, soldQuantity, earnings, percentages} =
    processCustomerOverviewData(customerOverviewData)

  const handleDateChange = (startDate: any, endDate: any) => {
    onDateChangeOverviewData(startDate, endDate)
    onDateChangeProductPerformance(startDate, endDate)
    onDateChange(startDate, endDate)
    onStateDateChange(startDate, endDate)
    onTopCustomersDateChange(startDate, endDate)
    setDateRange({
      startDate,
      endDate,
    })
  }

  const handleTypeChange = (type: string) => {
    setChartType(type)
    onPeriodChange(type)
  }

  const handleTopCustomersStateChange = (state: string) => {
    setTopCustomerState(state)
    onTopCustomersStateChange(state)
  }

  const handleSKUStateChange = (state: string) => {
    setSkuReportState(state)
    onSKUReportStateChange(state)
  }

  const contextValue = {
    productName,
    dateRangeLable,
    setDateRangeLable,
    dateRange,
    handleDateChange,
    handleTypeChange,
    chartType,
    options,
    data,
    parsedOverviewData,
    orders,
    soldQuantity,
    earnings,
    percentages,
    ProductPerformanceData,
    isLoadingChartData,
    topCustomerData: topCustomer,
    isTopCustomerLoading: isTopCustomersLoading,
    topStateData: topState,
    isTopStateLoading,
    topSkuData: topSku,
    isTopSkuLoading,
    skuDistributionData: DistributionData,
    isSkuDistributionLoading,
    priceReportData,
    isPriceReportLoading,
    skuReportData,
    isSKUReportLoading: isTopSkuLoading,
    onSKUStateChange: handleSKUStateChange,
    mainProductByState,
    topCustomersData,
    isTopCustomersLoading,
    AllStateData,
    onTopCustomersStateChange: handleTopCustomersStateChange,
    isMainProductByStateLoading: isTopStateLoading,
    onMainProductByStateSortingChange: () => {},
    onPriceReportSortingChange: () => {},
    onSKUReportSortingChange: () => {},
    onTopCustomersSortingChange: () => {},
  }

  return (
    <AnalyticsDetailsContext.Provider value={contextValue}>
      <div className='layout-sold-product-inner'>
        <Header />
        <Stats />
        <Chart />

        <div className='mb-10'>
          <PriceReportTable />
        </div>
        <div className='mb-10 '>
          <SKUReportTable />
        </div>
        <div className='mb-10 card border p-7'>
          <h2 className='text-dark fw-bolder me-5 mb-0'>Main Product Sales by State</h2>
          <MainProductReportNav />
        </div>
        <div className='mb-10'>
          <TopCustomersTable />
        </div>
      </div>
    </AnalyticsDetailsContext.Provider>
  )
}

export default PDAChart
