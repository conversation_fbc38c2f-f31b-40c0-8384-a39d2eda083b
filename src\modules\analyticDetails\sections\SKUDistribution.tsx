import {useContext} from 'react'
import PieChart from '../../../components/charts/PieChart'
import {PieCharData, pieChartOptions} from '../utils'
import SectionLoading from '../../loading/section-loading'
import {AnalyticsDetailsContext} from '../contexts'

const SKUDistribution = () => {
  const {isSkuDistributionLoading, skuDistributionData, percentages, soldQuantity} =
    useContext(AnalyticsDetailsContext)

  const piedata = skuDistributionData?.pieChartData?.piedata
  const pielabel = skuDistributionData?.pieChartData?.pielabel

  return (
    <div className='col-12 col-md-6 col-xxl-4 mt-0 sold-product-round-chart-section'>
      <div className='card border card-flush h-xl-100'>
        <div className='card-body p-7 d-flex flex-column justify-content-between'>
          <div className='mb-10'>
            <div className='d-flex align-items-start justify-content-between'>
              <h3 className='mb-0 fw-bolder '>SKU Distribution</h3>
              {isSkuDistributionLoading && <SectionLoading />}
            </div>
          </div>
          {skuDistributionData ? (
            <>
              <div className='d-flex justify-content-center mb-10'>
                <div className='col-xxl-7 col-xl-12 col-lg-12 mt-10 position-relative'>
                  <PieChart data={PieCharData({piedata, pielabel})} options={pieChartOptions} />
                  <div className='position-absolute top-50 start-50 translate-middle text-center'>
                    <p className='fs-2hx fw-bold mb-0'>
                      {skuDistributionData?.distributionData?.total_sold || 0}
                    </p>
                    <span className='fw-semibold text-gray-500'>Total Sold</span>
                  </div>
                </div>
              </div>
              <div className=''>
                <div className='row align-items-start justify-content-between'>
                  {soldQuantity?.map((item: any, index: number) => (
                    <div className='col-4' key={index}>
                      <span className=' d-flex fs-2x fw-bold'>{item.value}</span>
                      <h6 className='fs-6 fw-semibold text-gray-500'>{percentages?.[item.type]}</h6>
                    </div>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <span className='fw-semibold text-gray-500'>No Data Available</span>
          )}
        </div>
      </div>
    </div>
  )
}

export default SKUDistribution
