import React, {ReactNode} from 'react'

interface ButtonProps {
  /**
   * Available variants to use
   */
  variant: 'light' | 'primary' | 'danger'

  /**
   * What sizes to use
   */
  size: 'sm' | 'lg'

  /**
   * Pass either string or ReactNode as children
   */
  children: string | ReactNode
}

function Button({variant = 'primary', size = 'lg', children, ...props}: ButtonProps) {
  return (
    <button className={`btn btn-${variant} btn-${size}`} {...props}>
      {children}
    </button>
  )
}

export default Button
