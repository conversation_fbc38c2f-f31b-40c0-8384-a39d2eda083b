/* eslint-disable react-hooks/exhaustive-deps */
import {useEffect} from 'react'
import Select from '../../components/Select/Select'
import PredefinedDateRange from '../../components/DateRangePicker/PreDefinedDateRange'
import LineChart from '../../components/charts/LineChart'
import {ConfirmationModal} from '../../components/ConfirmationModal'
import Loading from '../../modules/loading'
import {ENTITY_PERFORMANCE_CHART_VIEW_OPTIONS} from './utils'

const EntityPerformanceChartModal = ({
  onClose,
  entityId,
  entityName,
  onEntityPerformanceChartIdChange,
  onEntityPerformanceChartViewChange,
  onEntityPerformanceChartDateRangeChange,
  entityPerformanceChartFilters,
  isEntityPerformanceChartDataLoading,
  entityPerformanceChartData,
}: {
  onClose: () => void
  entityId: string
  entityName: string
  onEntityPerformanceChartIdChange: (id: string) => void
  onEntityPerformanceChartViewChange: (e: any) => void
  onEntityPerformanceChartDateRangeChange: (startDate: any, endDate: any) => void
  entityPerformanceChartFilters: any
  isEntityPerformanceChartDataLoading: boolean
  entityPerformanceChartData: any
}) => {
  useEffect(() => {
    onEntityPerformanceChartIdChange(entityId)
  }, [entityId])

  const options = {
    responsive: true,
    layout: {
      padding: {
        bottom: 40,
      },
    },
    plugins: {
      legend: {
        display: true,
        position: 'top' as const,
      },
      tooltip: {
        enabled: true,
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: function (context: any) {
            let label = context.dataset.label || ''
            if (label) {
              label += ': '
            }
            if (context.parsed.y !== null) {
              label += `$${context.parsed.y.toLocaleString()}`
            }
            return label
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: false,
        },
        ticks: {
          callback: (value: string | number) =>
            typeof value === 'number'
              ? `$${value.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}`
              : value,
        },
      },
      x: {
        type: 'category' as const,
        grid: {
          display: false,
        },
        ticks: {
          padding: 15,
        },
      },
    },
    elements: {
      line: {
        tension: 0,
      },
      point: {
        radius: 4,
      },
    },
  }

  return (
    <ConfirmationModal
      show={true}
      onClose={onClose}
      title={entityName}
      dialogClassName='modal-dialog modal-lg mw-1000px'
      disableAction={true}
      disableFooter={true}
      body={
        <>
          <div className='d-flex justify-content-end align-items-end gap-5 mb-10'>
            <PredefinedDateRange
              id={`date-range-${entityId}`}
              defaultValue='Last 30 Days'
              includeCurrantDay={true}
              minPastDaysAllowed={455}
              cb={onEntityPerformanceChartDateRangeChange}
              exclude={['Today', 'Yesterday']}
            />
            <div className='d-flex align-items-center gap-3'>
              <Select
                id='view-select'
                options={ENTITY_PERFORMANCE_CHART_VIEW_OPTIONS(
                  entityPerformanceChartFilters?.start_date,
                  entityPerformanceChartFilters?.end_date
                )}
                onChange={onEntityPerformanceChartViewChange}
                className='w-100px'
                value={entityPerformanceChartFilters?.period}
              />
            </div>
          </div>

          <div className='card border'>
            <div className='card-body position-relative d-flex justify-content-center align-items-center min-h-350px p-7 '>
              {isEntityPerformanceChartDataLoading && <Loading />}
              <LineChart data={entityPerformanceChartData} options={options} />
            </div>
          </div>
        </>
      }
    />
  )
}

export default EntityPerformanceChartModal
