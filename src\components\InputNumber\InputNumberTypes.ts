export default interface InputNumberTypes extends React.InputHTMLAttributes<HTMLInputElement> {
  id: string
  name?: string
  placeholder?: string
  className?: string
  inputClass?: string
  value?: any
  error?: any
  label?: string
  register?: any
  isRequired?: boolean
  min?: number
  max?: number
  isFloat?: boolean
  decimalLimit?: number
  isRoundOff?: boolean
  maxLimit?: number
  isFormatted?: boolean
  minLimit?: number
  totalLimit?: number
  onChange?: (e: any) => void
  onBlur?: (e: any) => void
  onKeyDown?: (e: any) => void
  inputNumberRef?: any
  isReadOnly?: boolean
  disabled?: boolean
  maxLength?: number
  onFocus?: any
  isErrorWarning?: boolean
  setIsValid?: (value: any) => void
  customErrorMessage?: {
    minLimitError?: (limit: any) => any
    maxLimitError?: (limit: any) => any
    totalLimitError?: (limit: any) => any
  }
  errorClass?: string
  isCheckValidationOnLoad?: boolean
  labelClass?: string
}
