export interface DndTableProps {
  id: string
  data: any
  columns: any
  uniqueId: string
  TableRow: React.ComponentType<{row: any; dndSettings: any}>
  isLoading?: boolean
  onChange?: (data: any) => void
  readOnly?: boolean
  noDataText?: React.ReactNode
  permissionPath?: string
  hasWritePermission?: boolean
  searchValue?: string
  noSearchResultMessage?: string
  tableRowProps?: any
  dragDisable?: boolean
}
