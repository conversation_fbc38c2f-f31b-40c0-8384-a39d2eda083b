export default interface InputPriceTypes extends React.InputHTMLAttributes<HTMLInputElement> {
  id: string
  name?: string
  placeholder?: string
  className?: string
  inputClass?: string
  value?: any
  error?: any
  label?: string
  register?: any
  isRequired?: boolean
  min?: number
  max?: number
  priceSymbol?: string
  isFloat?: boolean
  decimalLimit?: number
  isRoundOff?: boolean
  maxLimit?: number
  minLimit?: number
  totalLimit?: number,
  onChange?: (e: any) => void
  onBlur?: (e: any) => void
  onKeyDown?: (e: any) => void
  inputPriceRef?: any
  isReadOnly?: boolean
  disabled?: boolean
  isFormatted?: boolean
  maxLength?: number
  onFocus?: any
  isErrorWarning?: boolean
  setIsValid?: (value: any) => void
  customErrorMessage?: {
    minLimitError?: (limit: any) => any
    maxLimitError?: (limit: any) => any
    totalLimitError?: (limit: any) => any
  }
  defaultValue?: any
  errorClass?: string
  isCheckValidationOnLoad?: boolean
  labelClass?: string
}
