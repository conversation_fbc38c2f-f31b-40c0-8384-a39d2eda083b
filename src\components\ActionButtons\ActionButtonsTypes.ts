export interface IActionButtons {
  className?: string
  show?: boolean
  disabled?: boolean
  setAction?: (action: string | null) => void
  buttons: {
    id: string
    label: string | React.ReactNode
    onClick?: () => void
    labelIcon?: React.ReactNode
    className?: string
    size?: 'sm' | 'md' | 'lg'
    loadingLabel?: string
    isLoading?: boolean
    loadingIcon?: string
    show?: boolean
    disabled?: boolean
    type?: 'button' | 'submit' | 'reset'
  }[]
}
