declare module 'daterangepicker' {
  interface DateRangePickerOptions {
    // Declare the options here based on your usage
    // For example:
    startDate?: moment.Moment;
    endDate?: moment.Moment;
    ranges?: {
      [rangeLabel: string]: [moment.Moment, moment.Moment];
    };
  }

  interface DateRangePicker {
    daterangepicker(options?: DateRangePickerOptions): void;
    // Declare other methods or properties you use here
  }

  interface JQuery {
    daterangepicker: DateRangePicker;
  }
}
  