import {useState} from 'react'
import {InputPasswordTypes} from './InputTypes'

function InputPassword({
  id,
  name,
  placeholder,
  error,
  label,
  register,
  isRequired,
  maxLength,
  className = '',
  inputClass = '',
  disabled = false,
  isShowPasswordIcon = true,
  labelClass = '',
  readOnly = false,
  ...rest
}: InputPasswordTypes) {
  const [showPassword, setShowPassword] = useState(false)
  return (
    <div className={className}>
      {label && (
        <label className={`form-label ${labelClass} ${isRequired ? 'required' : ''}`}>
          {label}
        </label>
      )}
      <div className='input-group'>
        <input
          id={id}
          name={name}
          placeholder={placeholder}
          type={showPassword ? 'text' : 'password'}
          autoComplete='off'
          className={`form-control  ${inputClass} ${
            error
              ? `is-invalid ${isShowPasswordIcon ? 'border-end-0' : 'rounded-end rounded-end-1'}`
              : ''
          } ${disabled ? 'text-gray-600' : ''}`}
          disabled={disabled}
          maxLength={maxLength}
          readOnly={readOnly}
          {...rest}
          {...register}
        />
        {isShowPasswordIcon && (
          <span
            data-name={name}
            className={`input-group-text symbol-label bg-light cursor-pointer ${
              error ? 'border-danger rounded-end rounded-end-1 border-start-1' : ''
            }`}
            onClick={() => setShowPassword(!showPassword)}
          >
            <i data-name={name} className={showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'}></i>
          </span>
        )}
        {error && error.message && <div className='invalid-feedback'>{error.message}</div>}
      </div>
    </div>
  )
}

export default InputPassword
