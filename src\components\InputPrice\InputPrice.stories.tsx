import { Meta, StoryFn } from '@storybook/react';
import InputPrice from './InputPrice';
import InputPriceTypes from './InputPriceTypes';

export default {
  title: 'Components/InputPrice',
  component: InputPrice,
} as Meta;

const Template: StoryFn<InputPriceTypes> = (args) => <InputPrice {...args} />;

export const Default = Template.bind({});
Default.args = {
  id: 'price',
  name: 'price',
  placeholder: 'Enter price',
  label: 'Price',
  isRequired: true,
  register: () => {}, // Mock register function
};

export const WithError = Template.bind({});
WithError.args = {
  id: 'price',
  name: 'price',
  placeholder: 'Enter price',
  label: 'Price',
  isRequired: true,
  error: { message: 'This field is required' },
  register: () => {}, // Mock register function
};
