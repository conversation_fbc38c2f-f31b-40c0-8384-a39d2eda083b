import clsx from 'clsx'
import React, {forwardRef} from 'react'
import '../../scss/TableTreeItemWrapper.scss'
import {TreeItemComponentProps} from '../../types/index'

export const TableTreeItemWrapper = forwardRef<
  HTMLDivElement,
  React.PropsWithChildren<TreeItemComponentProps<{}>>
>((props: any, ref) => {
  const {
    clone,
    depth,
    disableSelection,
    disableInteraction,
    disableSorting,
    ghost,
    handleProps,
    indentationWidth,
    indicator,
    collapsed,
    onCollapse,
    onRemove,
    item,
    wrapperRef,
    style,
    hideCollapseButton,
    childCount,
    manualDrag,
    showDragHandle,
    disableCollapseOnItemClick,
    isLast,
    parent,
    className,
    contentClassName,
    isOver,
    isOverParent,
    setCheckbox,
    isChecked,
    onCheckboxChange,
    indentedCheckbox,
    isHeadChecked,
    setIsHeadChecked,
    ...rest
  } = props

  const itemId = item?.id ?? '' // Ensure item.id is defined
  const isCheckedItemId = isChecked?.[itemId] || false // Defensive access

  return (
    <tr
      ref={wrapperRef}
      {...rest}
      className={clsx(
        'dnd-sortable-table-tree_wrapper',
        clone && 'dnd-sortable-table-tree_clone',
        ghost && 'dnd-sortable-table-tree_ghost',
        disableSelection && 'dnd-sortable-table-tree_disable-selection',
        disableInteraction && 'dnd-sortable-table-tree_disable-interaction',
        className
      )}
      style={{
        ...style,
        ...(indentedCheckbox
          ? {paddingLeft: clone ? indentationWidth : indentationWidth * depth}
          : {}),
      }}
    >
      {setCheckbox && !indentedCheckbox && (
        <td className='w-30px'>
          <div className='form-check form-check-custom form-check-sm'>
            <input
              className='form-check-input cursor-pointer'
              type='checkbox'
              checked={isCheckedItemId}
              onChange={() => onCheckboxChange(itemId)}
              id={`kt_check_indeterminate_${itemId}`}
            />
          </div>
        </td>
      )}

      <td
        className={clsx(`dnd-sortable-table-tree_tree-item`, contentClassName)}
        ref={ref}
        {...(manualDrag ? undefined : handleProps)}
        style={{
          paddingLeft: clone
            ? indentationWidth
            : indentationWidth * depth +
              (!(!manualDrag && !hideCollapseButton && onCollapse && childCount) && parent
                ? 23
                : 0),
        }}
        role='none'
      >
        <div className='d-flex align-items-center'>
          {!disableSorting && showDragHandle !== false && (
            <div
              className={`dnd-sortable-table-tree_handle me-6 opacity-20 ${
                !(!manualDrag && !hideCollapseButton && !!onCollapse && !!childCount) &&
                !parent &&
                'ms-0'
              }`}
              {...handleProps}
            >
              <i className='fa-solid fa-grip-vertical text-gray-700'></i>
            </div>
          )}

          {!manualDrag && !hideCollapseButton && !!onCollapse && !!childCount && (
            <button
              onClick={disableCollapseOnItemClick ? undefined : onCollapse}
              className={`dnd-sortable-table-tree_expand-table-tree-btn me-4 ms-2`}
            >
              {collapsed ? (
                <i className='bi bi-plus-circle fs-3 fw-semibold cursor-pointer'></i>
              ) : (
                <i className='bi bi-dash-circle fs-3 fw-semibold cursor-pointer'></i>
              )}
            </button>
          )}

          {setCheckbox && indentedCheckbox && (
            <div
              className='w-35px'
              style={{
                marginLeft:
                  disableSorting &&
                  !parent &&
                  !(!manualDrag && !hideCollapseButton && onCollapse && childCount)
                    ? 23
                    : 0,
              }}
            >
              <div className='form-check form-check-custom form-check-sm'>
                <input
                  className='form-check-input cursor-pointer'
                  type='checkbox'
                  checked={isCheckedItemId}
                  onChange={() => onCheckboxChange(itemId)}
                  id={`kt_check_indeterminate_${itemId}`}
                />
              </div>
            </div>
          )}

          <div data-no-dnd='true'>
            {props?.children?.length > 1 ? props?.children[0] : props?.children}
          </div>
        </div>
      </td>

      {props?.children?.length > 1 &&
        props?.children
          ?.filter((_: any, index: any) => index !== 0)
          ?.map((child: any, index: any) => <React.Fragment key={index}>{child}</React.Fragment>)}
    </tr>
  )
}) as <T>(
  p: React.PropsWithChildren<TreeItemComponentProps<T> & React.RefAttributes<HTMLDivElement>>
) => React.ReactElement
