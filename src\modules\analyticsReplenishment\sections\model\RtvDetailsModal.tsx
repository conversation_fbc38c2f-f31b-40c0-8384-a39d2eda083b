import React, {useEffect} from 'react'
import {ConfirmationModal} from '../../../../components/ConfirmationModal'
import {DynamicTable} from '../../../../components/DynamicTable'
import useGetRtvDetails from '../../hooks/useGetRtvDetails'
import Date from '../../../../components/Date/Date'
import {getBadgeColor} from '../../../../utils/badge'

export interface RtvDetailsModalProps {
  show: boolean
  onClose: () => void
  variantSku?: string
  title?: string
  dialogClassName?: string
}

const RtvDetailsModal: React.FC<RtvDetailsModalProps> = ({
  show,
  onClose,
  variantSku,
  title = 'RTV Details',
  dialogClassName = 'modal-lg',
}) => {
  const {rtvDetails, isLoading, fetchRtvDetails} = useGetRtvDetails({variant_sku: variantSku})

  useEffect(() => {
    if (show && variantSku) {
      fetchRtvDetails(variantSku)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show, variantSku])

  // Define table columns for RTV details
  const rtvTableColumns = [
    {
      key: 'date_time',
      label: 'Date & Time',
      headerStyle: 'min-w-120px',
      isSorted: false,
    },
    {
      key: 'rtv_number',
      label: 'RTV Number',
      headerStyle: 'min-w-150px',
      isSorted: false,
    },
    {
      key: 'company_name',
      label: 'Company Name',
      headerStyle: 'min-w-250px',
      isSorted: false,
    },
    {
      key: 'rtv_status',
      label: 'RTV Status',
      headerStyle: 'min-w-150px',
      isSorted: false,
    },
    {
      key: 'quantity',
      label: 'QTY',
      headerStyle: 'w-100px',
      isSorted: false,
    },
  ]

  // Prepare data for DynamicTable
  const tableData = Array.isArray(rtvDetails) ? rtvDetails : rtvDetails ? [rtvDetails] : []

  return (
    <ConfirmationModal
      show={show}
      onClose={onClose}
      dialogClassName={dialogClassName}
      title={
        <div className='d-flex align-items-center'>{variantSku && <span>{variantSku}</span>}</div>
      }
      bodyClass='mh-550px overflow-auto'
      body={
        <DynamicTable
          data={tableData}
          sortableColumns={rtvTableColumns}
          loading={isLoading}
          tableClass='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2'
          noDataMessage='No RTV data available for this variant SKU.'
          TableRow={({row}: any) => {
            return (
              <tr>
                <td>
                  <Date date={row?.rtv_date} />
                </td>
                <td>
                  <span className='text-gray-800 fw-bold fs-6'>{row?.rtv_number || '-'}</span>
                </td>
                <td>
                  <span className='text-gray-800 fs-6'>{row?.supplier_company_name || '-'}</span>
                </td>
                <td>
                  {row?.rtv_status ? (
                    <span className={`badge ${getBadgeColor(row?.rtv_status, 'light')} badge-lg`}>
                      <div className='align-items-center'>{row?.rtv_status}</div>
                    </span>
                  ) : (
                    '-'
                  )}
                </td>
                <td>
                  <span className='text-gray-800 fs-6'>
                    {row?.quantity || row?.qty || '0'}
                  </span>
                </td>
              </tr>
            )
          }}
        />
      }
      disableFooter={false}
      disableAction={true}
    />
  )
}

export default RtvDetailsModal
