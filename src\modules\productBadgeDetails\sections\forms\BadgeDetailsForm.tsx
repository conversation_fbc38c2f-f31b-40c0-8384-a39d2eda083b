import {useForm} from 'react-hook-form'
import {BadgeDetailsContext} from '../../context'
import {useContext, useState, useEffect} from 'react'
import {CheckBox} from '../../../../components/CheckBox'
import ProductSelectModal from '../modals/ProductSelectModal'
import {MultiSelect} from '../../../../components/MultiSelect/'
import InputColourPicker from '../../../../components/InputColourPicker'
import {MultiSelectTreeDropDown} from '../../../../components/MultiSelectTreeDropDown'
import {InputText} from '../../../../components/InputText'
import Loading from '../../../loading'

interface BadgeDetailsFormProps {
  initialValues?: any
  isEdit: boolean
}

const BadgeDetailsForm = ({isEdit}: BadgeDetailsFormProps) => {
  const {
    brands,
    categories,
    badgeDetails,
    isLoading,
    addProductBadge,
    updateProductBadge,
    isOperationLoading,
  } = useContext(BadgeDetailsContext)
  const [showProductModal, setShowProductModal] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const {
    register,
    setValue,
    watch,
    handleSubmit,
    reset,
    formState: {errors, dirtyFields},
    setError,
  } = useForm({
    defaultValues: {
      badgeName: '',
      tagLabel: '',
      brandIds: [] as any,
      categoryIds: [],
      indivisualProducts: [],
      bgColor: '#FFFFFF',
      fontColor: '#000000',
      productListing: true,
      productDetails: true,
    },
  })

  const selectedBrands = brands
  .filter((brand: any) => watch('brandIds')?.includes(brand.id))
  .map((brand: any) => ({
      label: brand.name,
      value: brand.id,
    }))
  // Refill form in edit mode when badgeDetails is loaded
  useEffect(() => {
    if (isEdit && badgeDetails) {
      reset({
        badgeName: badgeDetails.badgeName || '',
        tagLabel: badgeDetails.tagLabel || '',
        brandIds: badgeDetails.brandIds || [],
        categoryIds: badgeDetails.categoryIds || [],
        indivisualProducts: badgeDetails.indivisualProducts || [],
        bgColor: badgeDetails.bgColor || '#FFFFFF',
        fontColor: badgeDetails.fontColor || '#000000',
        productListing: badgeDetails.productListing ?? true,
        productDetails: badgeDetails.productDetails ?? true,
      })
    } else {
      reset()
    }
  }, [isEdit, badgeDetails, reset])

  const selectedProducts = watch('indivisualProducts')
  const backgroundColor = watch('bgColor')
  const fontColor = watch('fontColor')

  const handleProductsSelected = (products: any) => {
    setValue('indivisualProducts', products)
  }

  const onSubmit = handleSubmit(async (data) => {
    setIsSubmitting(true)
    try {
      if (isEdit) {
        updateProductBadge(data, badgeDetails.id, dirtyFields, setError)
      } else {
        addProductBadge(data, setError)
        reset()
      }
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      setIsSubmitting(false)
    }
  })

  return (
    <form className='form position-relative' onSubmit={onSubmit}>
      {/* Section 1: Badge Information and Style */}
      <div className='section mb-10 border-gray-300 border-2 p-5'>
        <h3 className='mb-8'>Badge Information and Style</h3>
        <div className='mb-7'>
          <div className='mb-7'>
            <InputText
              id='name'
              label='Product Badge'
              placeholder='Enter product badge'
              isRequired
              maxLength={100}
              error={errors.badgeName}
              register={register('badgeName', {required: true, maxLength: 100})}
              className='mb-2'
            />
            <div className='form-text'>
              Enter product badge details or describe the usage of the badge.
            </div>
            {errors.badgeName && (
              <div className='text-danger'>Product Badge is required (max 100 chars).</div>
            )}
          </div>
          <div className='mb-7'>
            <InputText
              id='tagLabel'
              label='Display Name'
              placeholder='Enter display name'
              isRequired
              maxLength={20}
              error={errors.tagLabel}
              register={register('tagLabel', {required: true, maxLength: 20})}
              className='mb-2'
            />
            <div className='form-text'>The entered value will be displayed with the product.</div>
            {errors.tagLabel && (
              <div className='text-danger'>Display Name is required (max 20 chars).</div>
            )}
          </div>
        </div>
        <div>
          <div className='mb-7'>
            <label className='form-label fw-bold mb-5'>Choose Badge Style</label>
            <div className='d-flex gap-5'>
              <InputColourPicker
                id='bgColor'
                name='bgColor'
                label='Background Color'
                value={backgroundColor}
                register={register('bgColor')}
                error={errors.bgColor}
                isHexOnly={true}
              />
              <InputColourPicker
                id='fontColor'
                name='fontColor'
                label='Font Color'
                value={fontColor}
                register={register('fontColor')}
                error={errors.fontColor}
                isHexOnly={true}
              />
            </div>
          </div>
          <div className='mb-7'>
            <label className='form-label fw-bold mb-5'>Badge Visibility</label>
            <div className='form-check mb-2'>
              <CheckBox
                id='showOnProductCard'
                name='productListing'
                label='Show a badge on the product card'
                register={register}
                error={errors.productListing}
                defaultChecked={badgeDetails?.productListing}
              />
              <div className='form-text'>
                If this is checked, a badge will be displayed on the product listing.
              </div>
            </div>
            <div className='form-check'>
              <CheckBox
                id='showOnProductDetail'
                name='productDetails'
                label='Show a badge on the product detail page'
                register={register}
                error={errors.productDetails}
                defaultChecked={badgeDetails?.showOnProductDetail}
              />
              <div className='form-text'>
                If this is enabled, a badge will be displayed on the product detail page.
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: Product Selection */}
      <div className='section mb-10 border-gray-300 border-2 p-5'>
        <h3 className='mb-8'>Product Selection</h3>
        <div>
          <div className='mb-7'>
            <label className='form-label fw-bold'>Choose Products by Category</label>
            <MultiSelectTreeDropDown
              id='categories'
              label='Categories'
              treeData={categories}
              labelKey='name'
              valueKey='id'
              placeholder='Select Categories'
              showTags={true}
              defaultValue={isEdit ? badgeDetails?.categoryIds || [] : []}
              onSubmit={(categories: any) => setValue('categoryIds', categories)}
              error={errors.categoryIds}
              isDisableExpand={false}
              isDefaultExpandAll={true}
              isClearable={true}
              isSearch={true}
              {...(isOperationLoading ? { selectedValues: watch('categoryIds') } : {})}
            />
          </div>
          <div className='mb-7'>
            <label className='form-label fw-bold'>Choose Brand</label>
            <MultiSelect
              id='brands'
              label='Brands'
              options={brands}
              labelKey='name'
              valueKey='id'
              valueType='number'
              placeholder='Select Brands'
              handleSelected={(values: any) =>
                setValue(
                  'brandIds',
                  values?.map((v: any) => v.value)
                )
              }
              error={errors.brandIds}
              {...(isOperationLoading ? { selectedValues: selectedBrands } :   {selectedValues: selectedBrands})}
              />
          </div>
        </div>
        <div>
          <div className='mb-7'>
            <label className='form-label fw-bold'>Choose a Product</label>
            <div className='d-flex gap-3 align-items-center'>
              <button
                type='button'
                className='btn btn-light-primary'
                onClick={() => setShowProductModal(true)}
              >
                Select Products
              </button>
              {selectedProducts?.length > 0 && (
                <span
                  className='text-muted text-decoration-underline cursor-pointer'
                  onClick={() => setShowProductModal(true)}
                  style={{cursor: 'pointer'}}
                >
                  {selectedProducts.length} products selected
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className='d-flex justify-content-end'>
        <button
          type='submit'
          className='btn btn-primary'
          disabled={isSubmitting || isOperationLoading}
        >
          {isSubmitting || isOperationLoading ? (
            <>
                Saving...
              <span
                className='spinner-border spinner-border-sm me-2'
                role='status'
                aria-hidden='true'
              ></span>
            </>
          ) : (
            <>Save Badge</>
          )}
        </button>
      </div>

      {isLoading && <Loading />}

      {showProductModal && (
        <ProductSelectModal
          onClose={() => setShowProductModal(false)}
          selectedProducts={selectedProducts}
          onProductsSelected={handleProductsSelected}
        />
      )}
    </form>
  )
}

export default BadgeDetailsForm
