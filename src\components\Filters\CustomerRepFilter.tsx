import {Controller} from 'react-hook-form'
import {useForm} from 'react-hook-form'

export default function CustomerRepFilter({onFilterChange, data}: any) {
  const {control} = useForm()

  const handleChange = (e: any) => {
    onFilterChange(e.target.value)
  }

  return (
    <div className='position-relative'>
      {/* <label className='form-check-label fw-semibold text-gray-900 me-3'>Representative:</label> */}
      <div className='w-200px'>
        <Controller
          control={control}
          name='order_status'
          render={({field: {name, value}}) => (
            <select
              className='form-select'
              data-kt-select2='true'
              data-placeholder='Select option'
              data-allow-clear='true'
              defaultValue={'0'}
              name={name}
              value={value}
              onChange={handleChange}
            >
              <option value=''>All Representative</option>
              {data &&  Object.keys(data)?.length > 0
                ? Object.keys(data)?.map((item: string, index: number) => (
                    <option key={index} value={data[item]}>
                      {data[item]}
                    </option>
                  ))
                : null}
            </select>
          )}
        />
      </div>
    </div>
  )
}