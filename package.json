{"name": "demo3", "version": "8.1.1", "private": true, "homepage": "", "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@formatjs/intl-pluralrules": "5.2.2", "@formatjs/intl-relativetimeformat": "11.2.2", "@fortawesome/fontawesome-free": "6.4.0", "@hookform/resolvers": "^3.1.0", "@lexical/file": "^0.18.0", "@lexical/react": "^0.18.0", "@popperjs/core": "2.11.7", "@react-oauth/google": "^0.11.0", "@reduxjs/toolkit": "^1.9.5", "@types/daterangepicker": "^3.1.5", "@types/draftjs-to-html": "^0.8.1", "@types/html-to-draftjs": "^1.4.0", "@types/jquery": "^3.5.16", "@types/lodash-es": "^4.17.12", "@types/pako": "^2.0.3", "@types/papaparse": "^5.3.7", "@types/react-datepicker": "^4.8.0", "@types/react-draft-wysiwyg": "^1.13.4", "@types/react-flatpickr": "^3.8.11", "@types/react-mentions": "^4.1.13", "animate.css": "4.1.1", "apexcharts": "3.40.0", "axios": "1.4.0", "bootstrap": "5.2.3", "bootstrap-icons": "^1.5.0", "chart.js": "^4.4.6", "chartjs-chart-geo": "^4.3.4", "clsx": "1.2.1", "d3-scale": "^4.0.2", "date-fns": "^2.30.0", "daterangepicker": "^3.1.0", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "formik": "2.2.9", "html-to-draftjs": "^1.5.0", "jodit-react": "^1.3.39", "jquery": "^3.7.0", "leaflet": "^1.9.4", "line-awesome": "1.3.0", "lodash-es": "^4.17.21", "multiselect-react-dropdown": "^2.0.25", "nouislider": "15.7.0", "pako": "^2.1.0", "papaparse": "^5.4.1", "prism-themes": "1.9.0", "prismjs": "1.29.0", "qs": "6.11.1", "react": "18.2.0", "react-animated-numbers": "^0.17.1", "react-apexcharts": "1.4.0", "react-bootstrap": "2.7.4", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "5.1.0", "react-datepicker": "^4.10.0", "react-dom": "18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.43.5", "react-infinite-scroll-component": "^6.1.0", "react-inlinesvg": "^3.0.1", "react-intl": "6.4.2", "react-leaflet": "^4.2.1", "react-mentions": "^4.4.10", "react-multi-select-component": "^4.3.4", "react-nestable": "^2.0.0", "react-query": "3.39.3", "react-redux": "^8.0.5", "react-router-dom": "6.11.1", "react-scripts": "^5.0.1", "react-select": "^5.7.4", "react-simple-wysiwyg": "^2.2.5", "react-table": "^7.7.0", "react-tagify": "^1.0.7", "react-toastify": "^9.1.1", "react-topbar-progress-indicator": "4.1.1", "redux-persist": "^6.0.0", "rsuite": "^5.40.0", "sass": "1.62.1", "smart-webcomponents-react": "^14.4.0", "socicon": "3.0.5", "socket.io-client": "^4.8.1", "tagify": "^0.1.1", "yjs": "^13.6.20", "yup": "1.1.1"}, "devDependencies": {"@storybook/addon-essentials": "^7.0.9", "@storybook/addon-interactions": "^7.0.9", "@storybook/addon-links": "^7.0.9", "@storybook/blocks": "^7.0.9", "@storybook/preset-create-react-app": "^7.0.8", "@storybook/react": "^7.0.9", "@storybook/react-webpack5": "^7.0.9", "@storybook/testing-library": "^0.1.0", "@testing-library/react": "14.0.0", "@testing-library/user-event": "14.4.3", "@types/bootstrap": "5.2.6", "@types/chart.js": "2.9.37", "@types/d3-scale": "^4.0.8", "@types/jest": "29.5.1", "@types/leaflet": "^1.9.14", "@types/node": "20.0.0", "@types/prismjs": "1.26.0", "@types/qs": "6.9.7", "@types/react": "18.2.5", "@types/react-copy-to-clipboard": "5.0.4", "@types/react-dom": "18.2.4", "@types/react-table": "^7.7.9", "babel-plugin-named-exports-order": "^0.0.2", "prettier": "2.8.8", "prop-types": "^15.8.1", "storybook": "^7.0.9", "typescript": "5.0.4", "webpack": "^5.82.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && echo '/* /index.html 200' | cat >build/_redirects ", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "prettier --check .", "format": "prettier --write .", "rtl": "webpack --config webpack-rtl.config.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "overrides": [{"files": ["**/*.stories.*"], "rules": {"import/no-anonymous-default-export": "off"}}]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintIgnore": ["dist/*"]}