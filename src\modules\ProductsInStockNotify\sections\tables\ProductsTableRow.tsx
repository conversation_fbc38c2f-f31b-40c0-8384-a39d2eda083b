import {useState} from 'react'
import {getExpandIcon, formatInquiryCount} from '../../utils'
import InquiryDetailsModal from '../modals/InquiryDetailsModal'
import {IInStockNotifyProduct} from '../../interfaces'
import ProductVariantsTable from './ProductVariantsTable'
import Date from '../../../../components/Date/Date'

const ProductsTableRow = ({row, columns}: {row: IInStockNotifyProduct; columns: any}) => {
  console.log('row: ', row);
  const [showInquiryModal, setShowInquiryModal] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const hasChildren = row?.hasChildren
  const variants = row?.variants || []

  return (
    <>
      <tr>
        <td>
          {hasChildren && (
            <button onClick={() => setIsExpanded(!isExpanded)} className='btn btn-icon btn-sm'>
              <i className={getExpandIcon(isExpanded, hasChildren)}></i>
            </button>
          )}
        </td>
        <td>
          <div className='d-flex align-items-center'>
            <span className={isExpanded ? 'fw-semibold text-dark ' : ''}>{row.productName}</span>
            {hasChildren && (
              <span className='badge badge-light-primary ms-2 fs-8'>
                {variants.length > 0 ? `${variants.length} variants` : 'Has variants'}
              </span>
            )}
          </div>
        </td>
        <td>
          <span className='text-gray-800 fw-normal'>{row.productSku}</span>
        </td>
        <td>
          <button
            onClick={() => setShowInquiryModal(true)}
            className='btn btn-link p-0 text-decoration-underline'
          >
            {formatInquiryCount(row.inquiryCount)}
          </button>
        </td>
        <td>
          <Date date={row.lastCreatedAt} />
        </td>
      </tr>

      {/* Product Variants table */}
      {isExpanded && variants.length > 0 && (
        <tr>
          <td colSpan={columns?.length}>
            <ProductVariantsTable productVariants={variants} productName={row.productName} />
          </td>
        </tr>
      )}

      {/* Parent Inquiry details modal */}
      {showInquiryModal && (
        <InquiryDetailsModal
          onClose={() => setShowInquiryModal(false)}
          row={row}
          isParentRow={true}
        />
      )}
    </>
  )
}

export default ProductsTableRow
