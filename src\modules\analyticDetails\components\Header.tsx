import {useContext} from 'react'
import PreDefinedDateRange from '../../../components/DateRangePicker/PreDefinedDateRange'
import {AnalyticsDetailsContext} from '../contexts'

const Header = () => {
  const {productName, handleDateChange, setDateRangeLable} = useContext(AnalyticsDetailsContext)

  const dateChangeCallback = (start: any, end: any) => {
    if (handleDateChange) {
      handleDateChange(start, end)
    }
  }

  return (
    <div className='mb-5 mt-5'>
      <div className='d-flex row'>
        <div className='col-xl-9 col-lg-7 col-md-7 mb-5'>
          <h4 className='fw-semibold mb-0'>{productName}</h4>
        </div>
        <div className='col-xl-3 col-lg-5 col-md-5 d-flex justify-content-end'>
          <div className='w-100'>
            <PreDefinedDateRange
              cb={dateChangeCallback}
              getLbl={setDateRangeLable}
              defaultValue={'This Month'}
              includeCurrantDay={true}
              isStoreDateInLocalStorage={false}
              minPastDaysAllowed={455}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default Header
