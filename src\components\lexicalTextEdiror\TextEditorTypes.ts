export interface TextEditorTypes {
    id: string
    setEditorKey?: (value: any) => void
    className?: string
    onChange?: (value: any) => void
    control?: any
    inputClass?: string
    registerKey?: string
    label?: string
    isRequired?: boolean
    error?: any
    errorClass?: string
    labelClass?: string
    errorMessageRef?: any
    readOnly?: boolean
    disabled?: any
    maxLength?: number
    rules?: any
    textEditorRef?: any
    value?: any
    defaultValue?: string | null
    hasDefaultValue?: boolean
    isChangeOnSave?: boolean
    isLoadingOperation?: boolean
    isLoadingData?: boolean
    isReadOnly?: boolean
    editorClass?: string
    isClearChanges?: boolean
    placeholder?: string
    onBlur?: (event: any) => void
    setIsValid?: (value: boolean) => void
    placeholderText?: string
  }
