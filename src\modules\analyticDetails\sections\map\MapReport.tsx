import React, {useContext, useEffect, useRef, useState} from 'react'
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON>er, Polygon, Toolt<PERSON>} from 'react-leaflet'
import 'leaflet/dist/leaflet.css'
import {scaleQuantize} from 'd3-scale'
import statesData from '../../json/data.json'
import {stateAbbreviations, transformData} from '../../utils/map'
import {AnalyticsDetailsContext} from '../../contexts'
import Loading from '../../../loading'
import L from 'leaflet'
import {formatToIndianNumber} from '../../../../utils/common'

function LegendControl({data}: any) {
  return (
    <div className='w-275px p-5 rounded bg-white border position-relative z-index-1'>
      <h4 className='mb-4'>Sold Product Report</h4>
      <div className='mh-500px overflow-auto'>
        <table className='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-1'>
          <thead className='table-row-bordered'>
            <tr className='fs-6 fw-semibold text-muted text-uppercase'>
              <th>State</th>
              <th>Sold Qty</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item: any) => (
              <tr key={item.state} className='fs-6'>
                <td>{item.state}</td>
                <td>
                  {item.total_quantity_by_range.toLocaleString()} ({item.percentage.toFixed(2)}%)
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default function MapReport() {
  const {
    dateRangeLable: period,
    mainProductByState,
    isMainProductByStateLoading: isLoading,
  } = useContext(AnalyticsDetailsContext)
  const [hoveredState, setHoveredState] = useState<string>('state')
  const salesData = transformData(mainProductByState)

  const getColor = (percentage: any) => {
    if (percentage === 0) {
      return '#A1A5B7'
    }
    const clampedPercentage = Math.max(0, Math.min(percentage, 100))

    const colorScale = scaleQuantize<string>()
      .domain([0, 100])
      .range(['#AED6F1', '#DAF7A6', '#FFC300', '#FF5733'])
    return colorScale(clampedPercentage)
  }

  const mapRef = useRef<any>(null)

  useEffect(() => {
    const mapElement = mapRef.current
    if (mapElement) {
      const handleWheel = (event: WheelEvent) => {
        event.stopPropagation()
      }

      mapElement.addEventListener('wheel', handleWheel)

      return () => {
        mapElement.removeEventListener('wheel', handleWheel)
      }
    }
  }, [])

  return (
    <>
      <div>
        {/* Map Section */}
        <div className='pb-5'>
          <div className='row d-flex align-items-center'>
            <div className='col-xl-9 col-md-8 d-flex flex-wrap align-items-center'>
              {/* <h2 className="text-dark fw-bolder me-5 mb-0 mb-5">SKU Report</h2> */}
            </div>

            <div className='col-xl-3 col-md-4 mb-5'>
              <div className='text-end sm-text-start'>
                <span>
                  Period: <strong>{period}</strong>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className='position-relative'>
          <MapContainer
            ref={mapRef}
            zoom={4.8}
            maxZoom={4.8}
            minZoom={4.8}
            zoomControl={false}
            dragging={true}
            doubleClickZoom={false}
            style={{height: '600px', width: '100%', backgroundColor: 'white'}}
            center={[37.8, -96]}
            attributionControl={false}
          >
            <LegendControl data={mainProductByState} />

            {statesData.features.map((state: any, index: any) => {
              const coordinates: any = state.geometry.coordinates[0].map((item: any) => [
                item[1],
                item[0],
              ])
              const stateName = state.properties.name
              const stateData = salesData[stateName]?.[0]
              const sales = stateData?.sales || 0
              const percentage = stateData?.percentage || 0
              const centroid = coordinates.reduce(
                (acc: any, coord: any) => {
                  acc.lat += coord[0]
                  acc.lng += coord[1]
                  return acc
                },
                {lat: 0, lng: 0}
              )
              const centroidLat = centroid.lat / coordinates.length
              const centroidLng = centroid.lng / coordinates.length
              const abbreviation = stateAbbreviations[stateName] || stateName

              // Validate centroid values to avoid NaN issues
              if (isNaN(centroidLat) || isNaN(centroidLng)) {
                console.warn(`Invalid centroid for state: ${stateName}`)
                return null // Skip rendering for this state
              }

              return (
                <Polygon
                  key={index}
                  pathOptions={{
                    fillColor: getColor(percentage),
                    fillOpacity: 0.7,
                    weight: 1,
                    opacity: 1,
                    dashArray: hoveredState === stateName ? 'none' : '1',
                    color: hoveredState === stateName ? 'black' : 'white',
                  }}
                  positions={coordinates}
                  eventHandlers={{
                    mouseover: () => {
                      setHoveredState(stateName)
                    },
                    mouseout: () => {
                      setHoveredState('state')
                    },
                  }}
                >
                  <Tooltip>
                    <span className='fs-6 fw-semibold'>
                      {stateName}: {formatToIndianNumber(sales)} ({percentage}%)
                    </span>
                  </Tooltip>
                  <Marker
                    key={`${stateName}-marker`}
                    position={[centroidLat, centroidLng]}
                    icon={L.divIcon({
                      className: 'state-label',
                      html: `<div style="font-size: 12px; font-weight: bold; text-align: center; color: black;">${abbreviation}</div>`,
                      iconSize: [40, 20], // Adjust size as needed
                    })}
                  />
                </Polygon>
              )
            })}
          </MapContainer>
          {isLoading && <Loading />}
        </div>
      </div>
    </>
  )
}
