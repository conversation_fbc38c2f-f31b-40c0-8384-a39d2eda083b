import {useContext} from 'react'
import {AnalyticsDetailsContext} from '../contexts'
import PDARenderChart from '../sections/PDARenderChart'

const Chart = () => {
  const {options, chartType, handleTypeChange, data} = useContext(AnalyticsDetailsContext)

  const onTypeChange = (type: string) => {
    if (handleTypeChange) {
      handleTypeChange(type)
    }
  }

  return (
    <div className='mb-10'>
      <ul className='nav nav-stretch nav-line-tabs nav-line-tabs-2x fs-5 fw-semibold flex-nowrap mb-10'>
        {options?.map((opt: any) => {
          return (
            <li className={`nav-item`} key={opt['value']}>
              <span
                className={`nav-link ${chartType === opt['value'] ? 'active' : ''}`}
                data-bs-toggle='tab'
                onClick={() => onTypeChange(opt['value'])}
              >
                {opt['label']}
              </span>
            </li>
          )
        })}
      </ul>

      <div className='border p-5 radius-10 mb-10'>
        <PDARenderChart chartType={chartType} combineData={data} />
      </div>
    </div>
  )
}

export default Chart
