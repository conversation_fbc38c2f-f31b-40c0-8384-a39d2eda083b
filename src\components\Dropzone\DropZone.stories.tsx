import {Meta, StoryFn} from '@storybook/react'
import Dropzone, {DropzoneProps} from './Dropzone'

export default {
  title: 'Components/Dropzone',
  component: Dropzone,
} as Meta

const Template: StoryFn<DropzoneProps> = (args) => <Dropzone {...args} />

export const Default = Template.bind({})
Default.args = {
  maxFileSize: 5,
}

export const CustomMaxFileSize = Template.bind({})
CustomMaxFileSize.args = {
  maxFileSize: 10,
}
