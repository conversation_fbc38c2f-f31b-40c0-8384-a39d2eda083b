.textEditor_ltr {
  text-align: left;
}
.textEditor_rtl {
  text-align: right;
}
.textEditor_h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 0.67em 0;
}
.textEditor_h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.83em 0;
}
.textEditor_h3 {
  font-size: 1.17em;
  font-weight: bold;
  margin: 1em 0;
}
.textEditor_h4 {
  font-size: 1em;
  font-weight: bold;
  margin: 1.67em 0;
}
.textEditor_h5 {
  font-size: 0.83em;
  font-weight: bold;
  margin: 2.5em 0;
}
.textEditor_h6 {
  font-size: 0.67em;
  font-weight: bold;
  margin: 3.5em 0;
}

.textEditor_paragraph {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
}

.textEditor_quote {
  margin: 0;
  margin-block-start: 1em;
  margin-block-end: 1em;
  font-size: 15px;
  color: rgb(101, 103, 107);
  border-left-color: rgb(206, 208, 212);
  border-left-width: 4px;
  border-left-style: solid;
  padding-left: 16px;
}

.textEditor_indent {
  --lexical-indent-base-value: 40px;
}
.textEditor_textBold {
  font-weight: bold;
}
.textEditor_textItalic {
  font-style: italic;
}
.textEditor_textUnderline {
  text-decoration: underline;
}
.textEditor_textStrikethrough {
  text-decoration: line-through;
}
.textEditor_textUnderlineStrikethrough {
  text-decoration: underline line-through;
}
.textEditor_textSubscript {
  font-size: 0.8em;
  vertical-align: sub !important;
}
.textEditor_textSuperscript {
  font-size: 0.8em;
  vertical-align: super;
}
.textEditor_textCode {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}
.textEditor_hashtag {
  background-color: rgba(88, 144, 255, 0.15);
  border-bottom: 1px solid rgba(88, 144, 255, 0.3);
}
.textEditor_link {
  color: rgb(33, 111, 219);
  text-decoration: none;
}
.textEditor_link:hover {
  text-decoration: underline;
  cursor: pointer;
}
.textEditor_code {
  background-color: rgb(240, 242, 245);
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 45px !important;
  line-height: 1.53;
  font-size: 13px;
  margin: 0;
  margin-top: 8px;
  margin-bottom: 8px;
  overflow-x: auto;
  position: relative;
  tab-size: 2;
}
.textEditor_code:before {
  content: attr(data-gutter);
  position: absolute;
  background-color: #eee;
  left: 0;
  top: 0;
  border-right: 1px solid #ccc;
  padding: 8px;
  color: #777;
  white-space: pre-wrap;
  text-align: right;
  min-width: 25px;
}
.textEditor_table {
  border-collapse: collapse;
  border-spacing: 0;
  overflow-y: scroll;
  overflow-x: scroll;
  table-layout: fixed;
  width: fit-content;
  margin: 0px 25px 30px 0px;
}
.textEditor_tableRowStriping tr:nth-child(even) {
  background-color: #f2f5fb;
}
.textEditor_tableSelection *::selection {
  background-color: transparent;
}
.textEditor_tableSelected {
  outline: 2px solid rgb(60, 132, 244);
}
.textEditor_tableCell {
  border: 1px solid #bbb !important;
  width: 75px;
  vertical-align: top;
  text-align: start;
  padding: 6px 8px;
  position: relative;
  outline: none;
}
.textEditor_tableCellSortedIndicator {
  display: block;
  opacity: 0.5;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: #999;
}
.textEditor_tableCellResizer {
  position: absolute;
  right: -4px;
  height: 100%;
  width: 8px;
  cursor: ew-resize;
  z-index: 10;
  top: 0;
}
.textEditor_tableCellHeader {
  background-color: #f2f3f5;
  text-align: start;
}
.textEditor_tableCellSelected {
  background-color: #c9dbf0;
}
.textEditor_tableCellPrimarySelected {
  border: 2px solid rgb(60, 132, 244);
  display: block;
  height: calc(100% - 2px);
  position: absolute;
  width: calc(100% - 2px);
  left: -1px;
  top: -1px;
  z-index: 2;
}
.textEditor_tableCellEditing {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
  border-radius: 3px;
}
.textEditor_tableAddColumns {
  position: absolute;
  background-color: #eee;
  height: 100%;
  animation: table-controls 0.2s ease;
  border: 0;
  cursor: pointer;
}
.textEditor_tableAddColumns:after {
  background-image: url(../icons/plus.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: block;
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
}
.textEditor_tableAddColumns:hover,
.textEditor_tableAddRows:hover {
  background-color: #c9dbf0;
}
.textEditor_tableAddRows {
  position: absolute;
  width: calc(100% - 25px);
  background-color: #eee;
  animation: table-controls 0.2s ease;
  border: 0;
  cursor: pointer;
  z-index: 1055;
}
.textEditor_tableAddRows:after {
  background-image: url(../icons/plus.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  display: block;
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
}
@keyframes table-controls {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.textEditor_tableCellResizeRuler {
  display: block;
  position: absolute;
  width: 1px;
  background-color: rgb(60, 132, 244);
  height: 100%;
  top: 0;
}
.textEditor_tableCellActionButtonContainer {
  display: block;
  right: 5px;
  top: 6px;
  position: absolute;
  z-index: 4;
  width: 20px;
  height: 20px;
}
.textEditor_tableCellActionButton {
  background-color: #eee;
  display: block;
  border: 0;
  border-radius: 20px;
  width: 20px;
  height: 20px;
  color: #222;
  cursor: pointer;
}
.textEditor_tableCellActionButton:hover {
  background-color: #ddd;
}
.textEditor_characterLimit {
  display: inline;
  background-color: #ffbbbb !important;
}
.textEditor_ol1 {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
  list-style-position: outside;
}
.textEditor_ol2 {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
  list-style-type: upper-alpha;
  list-style-position: outside;
}
.textEditor_ol3 {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
  list-style-type: lower-alpha;
  list-style-position: outside;
}
.textEditor_ol4 {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
  list-style-type: upper-roman;
  list-style-position: outside;
}
.textEditor_ol5 {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
  list-style-type: lower-roman;
  list-style-position: outside;
}
.textEditor_ul {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
  list-style-position: outside;
}
.textEditor_listItem {
  margin: 3px 32px;
}
.textEditor_listItemChecked,
.textEditor_listItemUnchecked {
  position: relative;
  margin-left: 8px;
  margin-right: 8px;
  padding-left: 24px;
  padding-right: 24px;
  list-style-type: none;
  outline: none;
}
.textEditor_listItemChecked {
  text-decoration: line-through;
}
.textEditor_listItemUnchecked:before,
.textEditor_listItemChecked:before {
  content: '';
  width: 16px;
  height: 16px;
  top: 2px;
  left: 0;
  cursor: pointer;
  display: block;
  background-size: cover;
  position: absolute;
}
.textEditor_listItemUnchecked[dir='rtl']:before,
.textEditor_listItemChecked[dir='rtl']:before {
  left: auto;
  right: 0;
}
.textEditor_listItemUnchecked:focus:before,
.textEditor_listItemChecked:focus:before {
  box-shadow: 0 0 0 2px #a6cdfe;
  border-radius: 2px;
}
.textEditor_listItemUnchecked:before {
  border: 1px solid #999;
  border-radius: 2px;
}
.textEditor_listItemChecked:before {
  border: 1px solid rgb(61, 135, 245);
  border-radius: 2px;
  background-color: #3d87f5;
  background-repeat: no-repeat;
}
.textEditor_listItemChecked:after {
  content: '';
  cursor: pointer;
  border-color: #fff;
  border-style: solid;
  position: absolute;
  display: block;
  top: 6px;
  width: 3px;
  left: 7px;
  right: 7px;
  height: 6px;
  transform: rotate(45deg);
  border-width: 0 2px 2px 0;
}
.textEditor_nestedListItem {
  list-style-type: none;
}
.textEditor_nestedListItem:before,
.textEditor_nestedListItem:after {
  display: none;
}
.textEditor_tokenComment {
  color: slategray;
}
.textEditor_tokenPunctuation {
  color: #999;
}
.textEditor_tokenProperty {
  color: #905;
}
.textEditor_tokenSelector {
  color: #690;
}
.textEditor_tokenOperator {
  color: #9a6e3a;
}
.textEditor_tokenAttr {
  color: #07a;
}
.textEditor_tokenVariable {
  color: #e90;
}
.textEditor_tokenFunction {
  color: #dd4a68;
}
.textEditor_mark {
  background: rgba(255, 212, 0, 0.14);
  border-bottom: 2px solid rgba(255, 212, 0, 0.3);
  padding-bottom: 2px;
}
.textEditor_markOverlap {
  background: rgba(255, 212, 0, 0.3);
  border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}
.textEditor_mark.selected {
  background: rgba(255, 212, 0, 0.5);
  border-bottom: 2px solid rgba(255, 212, 0, 1);
}
.textEditor_markOverlap.selected {
  background: rgba(255, 212, 0, 0.7);
  border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}
.textEditor_embedBlock {
  user-select: none;
}
.textEditor_embedBlockFocus {
  outline: 2px solid rgb(60, 132, 244);
}
.textEditor_layoutContainer {
  display: grid;
  gap: 10px;
  margin: 10px 0;
}
.textEditor_layoutItem {
  border: 1px dashed #ddd;
  padding: 8px 16px;
}
.textEditor_autocomplete {
  color: #ccc;
}
.textEditor_hr {
  padding: 2px 2px;
  border: none;
  margin: 1em 0;
  cursor: pointer;
}
.textEditor_hr:after {
  content: '';
  display: block;
  height: 2px;
  background-color: #ccc;
  line-height: 2px;
}
.textEditor_hr.selected {
  outline: 2px solid rgb(60, 132, 244);
  user-select: none;
}
