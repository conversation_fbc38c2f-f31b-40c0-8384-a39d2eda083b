import {createContext} from 'react'

interface IAnalyticsDetailsContext {
  productName?: string | null
  dateRangeLable?: string
  setDateRangeLable?: (lable: string) => void
  dateRange?: {
    startDate: string | null
    endDate: string | null
  }
  handleDateChange?: (startDate: any, endDate: any) => void
  handleTypeChange?: (type: string) => void
  chartType?: string
  options?: any[]
  data?: any
  parsedOverviewData?: any
  orders?: any[]
  soldQuantity?: any[]
  earnings?: any[]
  percentages?: any
  ProductPerformanceData?: any
  isLoadingChartData?: boolean
  topCustomerData?: any
  isTopCustomerLoading?: boolean
  topStateData?: any
  isTopStateLoading?: boolean
  topSkuData?: any
  isTopSkuLoading?: boolean
  skuDistributionData?: any
  isSkuDistributionLoading?: boolean
  priceReportData?: any[]
  isPriceReportLoading?: boolean
  onPriceReportSortingChange?: (field: string, direction: 'asc' | 'desc') => void
  priceReportFilters?: any
  skuReportData?: any[]
  isSKUReportLoading?: boolean
  onSKUReportSortingChange?: (field: string, direction: 'asc' | 'desc') => void
  onSKUStateChange?: (value: string) => void
  skuReportFilters?: any
  mainProductByState?: any[]
  isMainProductByStateLoading?: boolean
  onMainProductByStateSortingChange?: (field: string, direction: 'asc' | 'desc') => void
  mainProductByStateFilters?: any
  mainProductStates?: any[]
  topCustomersData?: any[]
  isTopCustomersLoading?: boolean
  onTopCustomersSortingChange?: (field: string, direction: 'asc' | 'desc') => void
  topCustomersFilters?: any
  AllStateData?: any[]
  onTopCustomersStateChange?: (value: string) => void
}

export const AnalyticsDetailsContext = createContext<IAnalyticsDetailsContext>({})

export const OrderHistoryPageContext = createContext({
  orderHistoryData: [],
  isLoading: false,
  orderFilters: {},
  onSortingChange: (key: string, value: string) => {},
})

export const PriceChangeLogPageContext = createContext({
  PriceChangeLogData: [],
  isLoading: false,
  priceChangeLogFilters: {},
  onSortingChange: (key: string, value: string) => {},
})