export interface InputPasswordTypes {
  id?: string
  name?: string
  placeholder?: string
  className?: string
  value?: string
  error?: any
  label?: string
  maxLength?: number
  isShowPasswordIcon?: boolean
  disabled?: boolean
  register?: any
  isRequired?: boolean
  onChange?: (e: any) => void
  onBlur?: (e: any) => void
  onKeyDown?: (e: any) => void
  inputClass?: string
  labelClass?: string
  readOnly?: boolean
}
