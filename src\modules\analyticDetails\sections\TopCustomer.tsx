import {useContext} from 'react'
import {KTSVG} from '../../../_metronic/helpers'
import {AnalyticsDetailsContext} from '../contexts'
import SectionLoading from '../../loading/section-loading'

function TopCustomer() {
  const {topCustomerData, isTopCustomerLoading} = useContext(AnalyticsDetailsContext)

  if (isTopCustomerLoading) {
    return <SectionLoading />
  }

  if (!topCustomerData) {
    return null
  }

  return (
    <div className='d-flex mb-5 align-items-center'>
      <div className='symbol symbol-50px w-40px h-40px bg-light-primary d-flex align-items-center justify-content-center me-4'>
        <i className='icon-24'>
          <KTSVG path='/media/ad-theme-icons/blue-doller-icon.svg' />
        </i>
      </div>
      <div>
        <h5 className='fw-bold mb-0 lh-1 mb-1'>Top Customer</h5>
        <span className='fs-6 fw-semibold text-gray-500 lh-1'>{topCustomerData?.name || '-'}</span>
      </div>
    </div>
  )
}

export default TopCustomer
