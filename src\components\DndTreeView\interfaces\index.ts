export interface DndTreeViewProps {
  data: any
  treeStructure: any
  onChange?: (data: any) => void
  setCheckbox?: boolean
  setHeaderCheckbox?: boolean
  disableSorting?: boolean
  autoSelectChildren?: boolean
  indentedCheckbox?: boolean
  isMultiSelect?: boolean
  onSelectCheckbox?: (data: any) => void
  getUpdatedDataOnly?: boolean
  isRefetchData?: boolean
  indentationWidth?: number
  isHeadChecked?: boolean
  setIsHeadChecked?: (isHeadChecked: any) => void
  checkBoxValues?: any
  setCheckboxValues?: (data: any) => void
}
