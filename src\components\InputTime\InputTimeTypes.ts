export interface InputTimeTypes extends React.InputHTMLAttributes<HTMLInputElement> {
  id: string
  popupPositionClass?: string
  days?: number
  hours?: number
  isHourConversion?: boolean
  defaultValue?: string
  isClearable?: boolean
  handleSelectedTime?: (time: string) => void
  isPopupDefaultValue?: boolean
  isTempAddition?: boolean
  isLoading?: boolean
  label?: string
  isRequired?: boolean
  labelClass?: string
  className?: string
  inputClass?: string
  error?: any
  errorClass?: string
  isReadOnly?: boolean
  isDisabled?: boolean
  isNormalInput?: boolean
}
