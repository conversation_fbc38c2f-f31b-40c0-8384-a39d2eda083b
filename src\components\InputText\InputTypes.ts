export interface InputTypes extends React.InputHTMLAttributes<HTMLInputElement> {
  id: string
  name?: string
  placeholder?: string
  className?: string
  inputClass?: string
  disabled?: boolean
  value?: any
  error?: any
  label?: string
  register?: any
  isRequired?: boolean
  maxLength?: number
  onBlur?: (e: any) => void
  onKeyDown?: (e: any) => void
  inputTextRef?: any,
  isReadOnly?: boolean
  onFocus?: any
  labelClass?: string
}
