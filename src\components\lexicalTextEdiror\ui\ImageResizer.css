.image-resizer-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.image-resizer {
  display: block;
  width: 7px;
  height: 7px;
  position: absolute;
  background-color: rgb(60, 132, 244);
  border: 1px solid #fff;
  pointer-events: all;
}

.image-resizer.image-resizer-n {
  top: -6px;
  left: 50%;
  cursor: n-resize;
}

.image-resizer.image-resizer-ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.image-resizer.image-resizer-e {
  bottom: 50%;
  right: -6px;
  cursor: e-resize;
}

.image-resizer.image-resizer-se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

.image-resizer.image-resizer-s {
  bottom: -6px;
  left: 50%;
  cursor: s-resize;
}

.image-resizer.image-resizer-sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.image-resizer.image-resizer-w {
  bottom: 50%;
  left: -6px;
  cursor: w-resize;
}

.image-resizer.image-resizer-nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.image-control-wrapper--resizing .image-resizer {
  display: block;
}
