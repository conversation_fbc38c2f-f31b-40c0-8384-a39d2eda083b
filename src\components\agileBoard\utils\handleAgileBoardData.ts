export function handleAgileBoardData(data: any) {
  const mergedData: any = []
  const {
    boardStructure = [],
    boardData: originalBoardData = [],
    isLoadingBoardStructure = false,
    isLoadingBoardData = false,
  } = data

  const boardData = isLoadingBoardData ? [] : originalBoardData

  boardStructure?.forEach((structureItem: any) => {
    const matchingData = boardData?.find(
      (dataItem: any) =>
        dataItem?.moduleName === structureItem?.moduleName &&
        dataItem?.columnName === structureItem?.columnName
    )

    if (matchingData) {
      mergedData?.push({
        ...structureItem,
        columnItems: matchingData?.columnItems,
      })
    } else {
      mergedData?.push({
        ...structureItem,
        columnItems: [],
      })
    }
  })

  const uniqueModuleNames = mergedData
    ?.map((item: any) => ({
      moduleName: item?.moduleName,
      moduleID: item?.moduleID,
    }))
    ?.filter(
      (value: any, index: any, self: any) =>
        self?.findIndex((obj: any) => obj?.moduleName === value?.moduleName) === index
    )

  const uniqueColumnNames = mergedData
    ?.map((item: any) => ({
      columnName: item?.columnName,
      cID: item?.cID,
    }))
    ?.filter(
      (value: any, index: any, self: any) =>
        self?.findIndex((obj: any) => obj?.columnName === value?.columnName) === index
    )

  const updatedTasksList = mergedData?.flatMap((column: any) =>
    column?.columnItems?.flatMap((task: any) => ({
      ...task,
    }))
  )

  const isLoading = isLoadingBoardStructure || isLoadingBoardData ? true : false

  return {mergedData, uniqueModuleNames, uniqueColumnNames, updatedTasksList, isLoading}
}
