import {InputTypes} from './InputTypes'

function Input({
  id,
  name,
  placeholder,
  error,
  label,
  register,
  isRequired,
  disabled = false,
  className = '',
  inputClass = '',
  maxLength,
  onChange,
  onBlur,
  inputTextRef,
  value,
  isReadOnly = false,
  labelClass = '',
  onClick,
  ...rest
}: InputTypes) {
  const {
    name: registerName = name,
    onChange: registerOnChange = onChange,
    onBlur: registerOnBlur = onBlur,
    ref: registerRef = inputTextRef,
  } = register && register.name ? register : {}

  const handleChange = (e: any) => {
    if (onChange) onChange(e)
    if (registerOnChange) registerOnChange(e)
  }

  const handleBlur = (e: any) => {
    if (onBlur) onBlur(e)
    if (registerOnBlur) registerOnBlur(e)
    e.target.value = e.target.value.trimEnd()
  }

  const handleRef = (e: any) => {
    if (inputTextRef)
      (inputTextRef as React.MutableRefObject<HTMLTextAreaElement | null>).current = e
    if (registerRef) registerRef(e)
  }

  return (
    <div className={className}>
      {label && (
        <label className={`form-label ${labelClass} ${isRequired ? 'required' : ''}`}>
          {label}
        </label>
      )}
      <input
        id={id}
        type='text'
        name={registerName || name}
        ref={handleRef}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={`form-control ${inputClass} ${error ? 'is-invalid' : ''} ${
          disabled ? 'text-gray-600' : ''
        }`}
        onInput={(e: any) => {
          let value = e.target.value.replace(/^\s+/, '')
          value = value.replace(/\s{2,}/g, ' ')
          e.target.value = value
        }}
        maxLength={maxLength}
        disabled={disabled}
        readOnly={isReadOnly}
        onClick={(e) => {
          if (onClick) {
            ;(e.currentTarget as HTMLInputElement).blur()
            onClick(e)
          }
        }}
        {...(value || value === '' ? {value: value} : {})}
        {...rest}
      />
      {error && error.message && <div className='invalid-feedback'>{error.message}</div>}
    </div>
  )
}

export default Input
