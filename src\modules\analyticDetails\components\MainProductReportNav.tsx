import React from 'react'
import Navigation from '../../../components/Navigation/Navigation'
import MapReport from '../sections/map/MapReport'
import {MainProductTable} from '../sections/table/MainProductTable'

const MainProductReportNav = () => {
  return (
    <Navigation
      baseUrl={window.location.pathname}
      className='mt-10 lh-2 fs-6'
      navigationData={[
        {
          key: 'list-view',
          label: 'List Report',
          component: <MainProductTable />,
        },
        {
          key: 'map-view',
          label: 'Map Report',
          component: <MapReport />,
        },
      ]}
    />
  )
}

export default MainProductReportNav
