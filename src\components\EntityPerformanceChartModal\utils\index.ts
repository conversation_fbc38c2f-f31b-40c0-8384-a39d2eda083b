export const ENTITY_PERFORMANCE_CHART_VIEW_OPTIONS = (startDate: string, endDate: string) => {
    const startDateObj = new Date(startDate)
    const endDateObj = new Date(endDate)
    const timeDiff = endDateObj.getTime() - startDateObj.getTime()
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))
  
    if (daysDiff <= 30) {
      // Up to 1 month
      return [
        {label: 'Day', value: 'day'},
        {label: 'Week', value: 'week'},
      ]
    } else if (daysDiff <= 180) {
      // Between 1 and 6 months
      return [
        {label: 'Week', value: 'week'},
        {label: 'Month', value: 'month'},
      ]
    } else {
      // More than 6 months
      return [
        {label: 'Month', value: 'month'},
        {label: 'Year', value: 'year'},
      ]
    }
  }