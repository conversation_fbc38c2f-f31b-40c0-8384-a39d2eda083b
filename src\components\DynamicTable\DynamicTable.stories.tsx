import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import DynamicTable from './DynamicTable';
import TableRow from './DynamicTableRow';

export default {
  title: 'Components/DynamicTable',
  component: DynamicTable,
} as Meta;

const Template: StoryFn<typeof DynamicTable> = (args) => <DynamicTable {...args} />;

export const Default = Template.bind({});
Default.args = {
  data: [
    { id: '1', name: '<PERSON>', email: '<EMAIL>' },
    { id: '2', name: '<PERSON>', email: '<EMAIL>' },
    { id: '3', name: '<PERSON>', email: '<EMAIL>' },
  ],
  sortableColumns: [
    { key: 'name', label: 'Name', isSorted: true, style: 'w-200px' },
    { key: 'email', label: 'Email', isSorted: true, style: 'w-300px' },
  ],
  TableRow: TableRow,
  onSortingChange: (key:any, direction:any) => console.log(`Sorting changed: ${key} - ${direction}`),
  
  
};

export const WithActionComponent = Template.bind({});
WithActionComponent.args = {
  data: [
    { id: '1', name: '<PERSON> Doe', email: '<EMAIL>' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
    { id: '3', name: 'Bob Johnson', email: '<EMAIL>' },
  ],
  sortableColumns: [
    { key: 'name', label: 'Name', isSorted: false, style: 'w-200px' },
    { key: 'email', label: 'Email', isSorted: false, style: 'w-300px' },
  ],
  TableRow: TableRow,
//   actionComponent: (row : any) => <button>View</button>,
  onSortingChange: (key:any, direction:any) => console.log(`Sorting changed: ${key} - ${direction}`),
 
};